import requests
from bs4 import BeautifulSoup
import re
import time
from urllib.parse import urljoin, urlparse
import logging

logger = logging.getLogger(__name__)

class WebScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.timeout = 10
        
    def get_webpage_info(self, url):
        """
        Get basic information about a webpage
        """
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract basic information
            title = soup.find('title')
            title_text = title.get_text().strip() if title else 'No title found'
            
            # Get meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            description = meta_desc.get('content', '').strip() if meta_desc else ''
            
            # Get all text content
            text_content = soup.get_text()
            word_count = len(text_content.split())
            
            return {
                'success': True,
                'url': url,
                'title': title_text,
                'description': description,
                'word_count': word_count,
                'status_code': response.status_code
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {url}: {str(e)}")
            return {'error': f'Failed to fetch webpage: {str(e)}'}
        except Exception as e:
            logger.error(f"Parsing error for {url}: {str(e)}")
            return {'error': f'Failed to parse webpage: {str(e)}'}
    
    def scrape_with_keywords(self, url, keywords):
        """
        Scrape a webpage and filter content based on keywords
        """
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract basic information
            title = soup.find('title')
            title_text = title.get_text().strip() if title else 'No title found'
            
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            description = meta_desc.get('content', '').strip() if meta_desc else ''
            
            # Get all text content
            text_content = soup.get_text()
            
            # Find keyword matches
            matches = self._find_keyword_matches(text_content, keywords)
            
            # Extract structured data
            structured_data = self._extract_structured_data(soup, keywords)
            
            return {
                'success': True,
                'url': url,
                'title': title_text,
                'description': description,
                'keywords': keywords,
                'matches': matches,
                'structured_data': structured_data,
                'total_matches': sum(len(match['snippets']) for match in matches),
                'status_code': response.status_code
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {url}: {str(e)}")
            return {'error': f'Failed to fetch webpage: {str(e)}'}
        except Exception as e:
            logger.error(f"Scraping error for {url}: {str(e)}")
            return {'error': f'Failed to scrape webpage: {str(e)}'}
    
    def _find_keyword_matches(self, text, keywords):
        """
        Find all occurrences of keywords in text with context
        """
        matches = []
        
        for keyword in keywords:
            keyword_matches = {
                'keyword': keyword,
                'count': 0,
                'snippets': []
            }
            
            # Case-insensitive search
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            
            for match in pattern.finditer(text):
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 100)
                
                snippet = text[start:end].strip()
                
                # Highlight the keyword in the snippet
                highlighted_snippet = pattern.sub(
                    f'**{keyword}**', 
                    snippet, 
                    count=0
                )
                
                keyword_matches['snippets'].append({
                    'text': highlighted_snippet,
                    'position': match.start()
                })
                keyword_matches['count'] += 1
            
            matches.append(keyword_matches)
        
        return matches
    
    def _extract_structured_data(self, soup, keywords):
        """
        Extract structured data like headings, links, etc. that contain keywords
        """
        structured_data = {
            'headings': [],
            'links': [],
            'images': [],
            'meta_tags': [],
            'emails': [],
            'phone_numbers': []
        }
        
        # Extract headings containing keywords
        for heading_tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            headings = soup.find_all(heading_tag)
            for heading in headings:
                heading_text = heading.get_text().strip()
                if self._contains_keywords(heading_text, keywords):
                    structured_data['headings'].append({
                        'tag': heading_tag,
                        'text': heading_text
                    })
        
        # Extract links containing keywords
        links = soup.find_all('a', href=True)
        for link in links:
            link_text = link.get_text().strip()
            if self._contains_keywords(link_text, keywords):
                structured_data['links'].append({
                    'text': link_text,
                    'href': link['href']
                })
        
        # Extract images with alt text containing keywords
        images = soup.find_all('img', alt=True)
        for img in images:
            alt_text = img.get('alt', '').strip()
            if self._contains_keywords(alt_text, keywords):
                structured_data['images'].append({
                    'alt': alt_text,
                    'src': img.get('src', '')
                })

        # Extract email addresses from the entire page
        structured_data['emails'] = self._extract_emails(soup)

        # Extract phone numbers from the entire page
        structured_data['phone_numbers'] = self._extract_phone_numbers(soup)

        return structured_data
    
    def _contains_keywords(self, text, keywords):
        """
        Check if text contains any of the keywords (case-insensitive)
        """
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in keywords)

    def _extract_emails(self, soup):
        """
        Extract email addresses from the webpage
        """
        emails = []

        # Get all text content
        text_content = soup.get_text()

        # Email regex pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

        # Find all email addresses
        found_emails = re.findall(email_pattern, text_content)

        # Also check in href attributes of mailto links
        mailto_links = soup.find_all('a', href=re.compile(r'^mailto:'))
        for link in mailto_links:
            href = link.get('href', '')
            if href.startswith('mailto:'):
                email = href.replace('mailto:', '').split('?')[0]  # Remove query parameters
                if email and email not in found_emails:
                    found_emails.append(email)

        # Remove duplicates and return with context
        unique_emails = list(set(found_emails))

        for email in unique_emails:
            # Find context around the email
            email_index = text_content.lower().find(email.lower())
            if email_index != -1:
                start = max(0, email_index - 50)
                end = min(len(text_content), email_index + len(email) + 50)
                context = text_content[start:end].strip()

                emails.append({
                    'email': email,
                    'context': context
                })

        return emails

    def _extract_phone_numbers(self, soup):
        """
        Extract phone numbers from the webpage
        """
        phone_numbers = []

        # Get all text content
        text_content = soup.get_text()

        # Phone number regex patterns
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # ************ or ************ or 1234567890
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',   # (*************
            r'\+\d{1,3}[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}',  # International format
        ]

        for pattern in phone_patterns:
            found_phones = re.findall(pattern, text_content)
            for phone in found_phones:
                if phone not in [p['phone'] for p in phone_numbers]:
                    # Find context around the phone number
                    phone_index = text_content.find(phone)
                    if phone_index != -1:
                        start = max(0, phone_index - 50)
                        end = min(len(text_content), phone_index + len(phone) + 50)
                        context = text_content[start:end].strip()

                        phone_numbers.append({
                            'phone': phone,
                            'context': context
                        })

        return phone_numbers
