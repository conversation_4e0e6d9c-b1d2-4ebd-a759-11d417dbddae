import re
from urllib.parse import urlparse

def validate_url(url):
    """
    Validate if a string is a valid URL
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False

def sanitize_keywords(keywords):
    """
    Sanitize and filter keywords
    """
    if not isinstance(keywords, list):
        return []
    
    sanitized = []
    for keyword in keywords:
        if isinstance(keyword, str):
            # Remove extra whitespace and convert to string
            clean_keyword = keyword.strip()
            if clean_keyword and len(clean_keyword) > 0:
                sanitized.append(clean_keyword)
    
    return sanitized

def format_snippet(text, max_length=200):
    """
    Format text snippet to a maximum length
    """
    if len(text) <= max_length:
        return text
    
    # Try to break at word boundaries
    truncated = text[:max_length]
    last_space = truncated.rfind(' ')
    
    if last_space > max_length * 0.8:  # If we can break at a word boundary
        return truncated[:last_space] + '...'
    else:
        return truncated + '...'

def extract_domain(url):
    """
    Extract domain from URL
    """
    try:
        parsed = urlparse(url)
        return parsed.netloc
    except Exception:
        return url
