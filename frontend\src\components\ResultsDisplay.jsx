import React, { useState } from 'react';

const ResultsDisplay = ({ results }) => {
  const [activeTab, setActiveTab] = useState('matches');

  if (!results || !results.success) {
    return null;
  }

  const { title, url, keywords, matches, structured_data, total_matches } = results;

  const tabs = [
    { id: 'matches', label: 'Keyword Matches', count: total_matches },
    { id: 'headings', label: 'Headings', count: structured_data?.headings?.length || 0 },
    { id: 'links', label: 'Links', count: structured_data?.links?.length || 0 },
    { id: 'images', label: 'Images', count: structured_data?.images?.length || 0 }
  ];

  const renderMatches = () => (
    <div className="space-y-6">
      {matches.map((match, index) => (
        <div key={index} className="border border-gray-200 rounded-lg p-4">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-semibold text-gray-900">
              "{match.keyword}"
            </h3>
            <span className="bg-indigo-100 text-indigo-800 text-sm font-medium px-2.5 py-0.5 rounded">
              {match.count} matches
            </span>
          </div>
          
          {match.snippets.length > 0 ? (
            <div className="space-y-3">
              {match.snippets.slice(0, 5).map((snippet, snippetIndex) => (
                <div key={snippetIndex} className="bg-gray-50 rounded p-3">
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {snippet.text.split('**').map((part, partIndex) => 
                      partIndex % 2 === 1 ? (
                        <mark key={partIndex} className="bg-yellow-200 px-1 rounded">
                          {part}
                        </mark>
                      ) : (
                        part
                      )
                    )}
                  </p>
                </div>
              ))}
              {match.snippets.length > 5 && (
                <p className="text-sm text-gray-500 italic">
                  ... and {match.snippets.length - 5} more matches
                </p>
              )}
            </div>
          ) : (
            <p className="text-gray-500 italic">No matches found for this keyword.</p>
          )}
        </div>
      ))}
    </div>
  );

  const renderHeadings = () => (
    <div className="space-y-3">
      {structured_data?.headings?.length > 0 ? (
        structured_data.headings.map((heading, index) => (
          <div key={index} className="border-l-4 border-indigo-500 pl-4 py-2">
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
              {heading.tag}
            </span>
            <p className="text-gray-900 font-medium">{heading.text}</p>
          </div>
        ))
      ) : (
        <p className="text-gray-500 italic">No headings containing keywords found.</p>
      )}
    </div>
  );

  const renderLinks = () => (
    <div className="space-y-3">
      {structured_data?.links?.length > 0 ? (
        structured_data.links.map((link, index) => (
          <div key={index} className="bg-gray-50 rounded p-3">
            <p className="text-gray-900 font-medium mb-1">{link.text}</p>
            <a 
              href={link.href} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-indigo-600 hover:text-indigo-800 text-sm break-all"
            >
              {link.href}
            </a>
          </div>
        ))
      ) : (
        <p className="text-gray-500 italic">No links containing keywords found.</p>
      )}
    </div>
  );

  const renderImages = () => (
    <div className="space-y-3">
      {structured_data?.images?.length > 0 ? (
        structured_data.images.map((image, index) => (
          <div key={index} className="bg-gray-50 rounded p-3">
            <p className="text-gray-900 font-medium mb-1">Alt text: {image.alt}</p>
            <p className="text-sm text-gray-600 break-all">Source: {image.src}</p>
          </div>
        ))
      ) : (
        <p className="text-gray-500 italic">No images with alt text containing keywords found.</p>
      )}
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'matches':
        return renderMatches();
      case 'headings':
        return renderHeadings();
      case 'links':
        return renderLinks();
      case 'images':
        return renderImages();
      default:
        return renderMatches();
    }
  };

  return (
    <div className="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        <p className="text-sm text-gray-600 mt-1">{url}</p>
        <div className="flex flex-wrap gap-2 mt-2">
          {keywords.map((keyword, index) => (
            <span 
              key={index}
              className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
            >
              {keyword}
            </span>
          ))}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default ResultsDisplay;
