#!/usr/bin/env python3
"""
Test script for the AI Web Scraper API
"""

import requests
import json

API_BASE_URL = "http://localhost:5000"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    response = requests.get(f"{API_BASE_URL}/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_preview(url):
    """Test the preview endpoint"""
    print(f"Testing preview for: {url}")
    data = {"url": url}
    response = requests.post(f"{API_BASE_URL}/scrape/preview", json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_scrape(url, keywords):
    """Test the scraping endpoint"""
    print(f"Testing scrape for: {url} with keywords: {keywords}")
    data = {"url": url, "keywords": keywords}
    response = requests.post(f"{API_BASE_URL}/scrape", json=data)
    print(f"Status: {response.status_code}")
    result = response.json()
    
    if result.get('success'):
        print(f"Title: {result['title']}")
        print(f"Total matches: {result['total_matches']}")
        print("Keyword matches:")
        for match in result['matches']:
            print(f"  - '{match['keyword']}': {match['count']} matches")
            if match['snippets']:
                print(f"    First snippet: {match['snippets'][0]['text'][:100]}...")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")
    print()

if __name__ == "__main__":
    print("AI Web Scraper API Test")
    print("=" * 40)
    
    # Test health check
    test_health_check()
    
    # Test preview
    test_preview("https://example.com")
    
    # Test scraping
    test_scrape("https://example.com", ["example", "domain", "information"])
    
    print("Tests completed!")
