# AI Web Scraper

A modern web application for scraping webpages and extracting content based on keywords. Built with Python Flask backend and React frontend.

## Features

- **Keyword-based scraping**: Extract content from webpages that contains specific keywords
- **Website preview**: Get basic information about a webpage before scraping
- **Structured data extraction**: Find keywords in headings, links, and image alt text
- **Modern UI**: Clean, responsive interface built with React and Tailwind CSS
- **Real-time results**: Display scraped content with highlighted keywords

## Project Structure

```
AI-Scrapper/
├── backend/
│   ├── app.py              # Flask API server
│   ├── scraper.py          # Web scraping logic
│   ├── utils.py            # Helper functions
│   └── requirements.txt    # Python dependencies
├── frontend/
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── App.jsx         # Main App component
│   │   └── main.js         # React entry point
│   ├── package.json        # Node.js dependencies
│   └── tailwind.config.js  # Tailwind CSS configuration
└── README.md
```

## Installation

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create a virtual environment (recommended):
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Start the Flask server:
   ```bash
   python app.py
   ```

The backend API will be available at `http://localhost:5000`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

The frontend will be available at `http://localhost:5173`

## Usage

1. **Start both servers**: Make sure both the Flask backend (port 5000) and React frontend (port 5173) are running.

2. **Open the web app**: Navigate to `http://localhost:5173` in your browser.

3. **Enter a URL**: Input the webpage URL you want to scrape.

4. **Preview (optional)**: Click "Preview" to see basic information about the webpage.

5. **Add keywords**: Enter keywords separated by commas (e.g., "python, programming, tutorial").

6. **Start scraping**: Click "Start Scraping" to extract content containing your keywords.

7. **View results**: Browse through the different tabs to see:
   - **Keyword Matches**: Text snippets containing your keywords
   - **Headings**: Page headings that contain keywords
   - **Links**: Links with text containing keywords
   - **Images**: Images with alt text containing keywords

## API Endpoints

### `GET /`
Health check endpoint.

### `POST /scrape`
Scrape a webpage for specific keywords.

**Request body:**
```json
{
  "url": "https://example.com",
  "keywords": ["keyword1", "keyword2"]
}
```

### `POST /scrape/preview`
Get basic information about a webpage.

**Request body:**
```json
{
  "url": "https://example.com"
}
```

## Technologies Used

### Backend
- **Flask**: Web framework
- **BeautifulSoup4**: HTML parsing and web scraping
- **Requests**: HTTP requests
- **Flask-CORS**: Cross-origin resource sharing

### Frontend
- **React**: UI framework
- **Vite**: Build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **Axios**: HTTP client

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.
