import React, { useState } from 'react';
import axios from 'axios';
import ScrapingForm from './components/ScrapingForm';
import ResultsDisplay from './components/ResultsDisplay';
import LoadingSpinner from './components/LoadingSpinner';

const API_BASE_URL = 'http://localhost:5000';

function App() {
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleScrape = async (url, keywords) => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await axios.post(`${API_BASE_URL}/scrape`, {
        url,
        keywords
      });

      setResults(response.data);
    } catch (err) {
      console.error('Scraping error:', err);
      setError(
        err.response?.data?.error || 
        'Failed to scrape webpage. Please check the URL and try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = async (url) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${API_BASE_URL}/scrape/preview`, {
        url
      });

      return response.data;
    } catch (err) {
      console.error('Preview error:', err);
      setError(
        err.response?.data?.error || 
        'Failed to preview webpage. Please check the URL and try again.'
      );
      return null;
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            AI Web Scraper
          </h1>
          <p className="text-lg text-gray-600">
            Extract content from webpages using keywords
          </p>
        </header>

        <div className="max-w-4xl mx-auto">
          <ScrapingForm 
            onScrape={handleScrape}
            onPreview={handlePreview}
            loading={loading}
          />

          {error && (
            <div className="mt-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Error
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    {error}
                  </div>
                </div>
              </div>
            </div>
          )}

          {loading && <LoadingSpinner />}

          {results && !loading && (
            <ResultsDisplay results={results} />
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
