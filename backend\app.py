from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
from scraper import WebScraper
from utils import validate_url, sanitize_keywords

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize the web scraper
scraper = WebScraper()

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'AI Web Scraper API is running'
    })

@app.route('/scrape', methods=['POST'])
def scrape_webpage():
    """
    Scrape a webpage for specific keywords
    
    Expected JSON payload:
    {
        "url": "https://example.com",
        "keywords": ["keyword1", "keyword2"]
    }
    """
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        url = data.get('url')
        keywords = data.get('keywords', [])
        
        # Validate inputs
        if not url:
            return jsonify({'error': 'URL is required'}), 400
        
        if not validate_url(url):
            return jsonify({'error': 'Invalid URL format'}), 400
        
        if not keywords or not isinstance(keywords, list):
            return jsonify({'error': 'Keywords must be a non-empty list'}), 400
        
        # Sanitize keywords
        keywords = sanitize_keywords(keywords)
        
        if not keywords:
            return jsonify({'error': 'No valid keywords provided'}), 400
        
        logger.info(f"Scraping URL: {url} for keywords: {keywords}")
        
        # Perform scraping
        result = scraper.scrape_with_keywords(url, keywords)
        
        if result.get('error'):
            return jsonify(result), 400
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in scrape_webpage: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/scrape/preview', methods=['POST'])
def preview_webpage():
    """
    Get basic information about a webpage without keyword filtering
    
    Expected JSON payload:
    {
        "url": "https://example.com"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        url = data.get('url')
        
        if not url:
            return jsonify({'error': 'URL is required'}), 400
        
        if not validate_url(url):
            return jsonify({'error': 'Invalid URL format'}), 400
        
        logger.info(f"Previewing URL: {url}")
        
        # Get basic webpage info
        result = scraper.get_webpage_info(url)
        
        if result.get('error'):
            return jsonify(result), 400
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in preview_webpage: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/scrape/emails', methods=['POST'])
def extract_emails():
    """
    Extract email addresses and phone numbers from a webpage

    Expected JSON payload:
    {
        "url": "https://example.com"
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        url = data.get('url')

        if not url:
            return jsonify({'error': 'URL is required'}), 400

        if not validate_url(url):
            return jsonify({'error': 'Invalid URL format'}), 400

        logger.info(f"Extracting emails from URL: {url}")

        # Get webpage info and extract emails
        result = scraper.get_webpage_info(url)

        if result.get('error'):
            return jsonify(result), 400

        # Get the full scraping result to extract emails
        scrape_result = scraper.scrape_with_keywords(url, ['email', 'contact', '@'])

        if scrape_result.get('error'):
            return jsonify(scrape_result), 400

        # Extract emails and phone numbers
        emails = scrape_result['structured_data'].get('emails', [])
        phone_numbers = scrape_result['structured_data'].get('phone_numbers', [])

        return jsonify({
            'success': True,
            'url': url,
            'title': result['title'],
            'emails': emails,
            'phone_numbers': phone_numbers,
            'total_emails': len(emails),
            'total_phone_numbers': len(phone_numbers)
        })

    except Exception as e:
        logger.error(f"Error in extract_emails: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
