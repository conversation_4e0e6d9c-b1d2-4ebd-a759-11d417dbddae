<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Web Scraper</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #333;
        text-align: center;
      }
      .form-group {
        margin-bottom: 20px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input[type="url"],
      input[type="text"] {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      button {
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
      }
      button:hover {
        background-color: #0056b3;
      }
      .results {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 4px;
        display: none;
      }
      .error {
        color: #dc3545;
        background-color: #f8d7da;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
      }
      .success {
        color: #155724;
        background-color: #d4edda;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>AI Web Scraper</h1>
      <p>Extract content from webpages using keywords</p>

      <form id="scraperForm">
        <div class="form-group">
          <label for="url">Website URL:</label>
          <input
            type="url"
            id="url"
            placeholder="https://example.com"
            required
          />
        </div>

        <div class="form-group">
          <label for="keywords">Keywords (comma-separated):</label>
          <input
            type="text"
            id="keywords"
            placeholder="keyword1, keyword2, keyword3"
            required
          />
        </div>

        <button type="button" onclick="previewWebsite()">Preview</button>
        <button type="button" onclick="extractEmails()">Extract Emails</button>
        <button type="submit">Start Scraping</button>
      </form>

      <div id="results" class="results"></div>
    </div>

    <script>
      const API_BASE_URL = "http://localhost:5000";

      document
        .getElementById("scraperForm")
        .addEventListener("submit", async function (e) {
          e.preventDefault();
          await scrapeWebsite();
        });

      async function previewWebsite() {
        const url = document.getElementById("url").value;
        if (!url) {
          showError("Please enter a URL first");
          return;
        }

        try {
          const response = await fetch(`${API_BASE_URL}/scrape/preview`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ url: url }),
          });

          const data = await response.json();

          if (data.success) {
            showSuccess(`Preview: ${data.title} (${data.word_count} words)`);
          } else {
            showError(data.error || "Failed to preview website");
          }
        } catch (error) {
          showError("Error connecting to server: " + error.message);
        }
      }

      async function extractEmails() {
        const url = document.getElementById("url").value;
        if (!url) {
          showError("Please enter a URL first");
          return;
        }

        try {
          showSuccess("Extracting emails and phone numbers...");

          const response = await fetch(`${API_BASE_URL}/scrape/emails`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ url: url }),
          });

          const data = await response.json();

          if (data.success) {
            displayEmailResults(data);
          } else {
            showError(data.error || "Failed to extract emails");
          }
        } catch (error) {
          showError("Error connecting to server: " + error.message);
        }
      }

      async function scrapeWebsite() {
        const url = document.getElementById("url").value;
        const keywordsText = document.getElementById("keywords").value;

        if (!url || !keywordsText) {
          showError("Please fill in all fields");
          return;
        }

        const keywords = keywordsText
          .split(",")
          .map((k) => k.trim())
          .filter((k) => k);

        try {
          showSuccess("Scraping in progress...");

          const response = await fetch(`${API_BASE_URL}/scrape`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ url: url, keywords: keywords }),
          });

          const data = await response.json();

          if (data.success) {
            displayResults(data);
          } else {
            showError(data.error || "Failed to scrape website");
          }
        } catch (error) {
          showError("Error connecting to server: " + error.message);
        }
      }

      function displayResults(data) {
        const resultsDiv = document.getElementById("results");
        let html = `
          <h3>Results for: ${data.title}</h3>
          <p><strong>URL:</strong> ${data.url}</p>
          <p><strong>Total matches:</strong> ${data.total_matches}</p>
          <h4>Keyword Matches:</h4>
        `;

        data.matches.forEach((match) => {
          html += `
            <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
              <strong>"${match.keyword}"</strong> - ${match.count} matches
              ${match.snippets
                .slice(0, 3)
                .map(
                  (snippet) =>
                    `<div style="margin-top: 5px; padding: 5px; background: #f8f9fa; font-size: 0.9em;">
                  ${snippet.text.replace(/\*\*(.*?)\*\*/g, "<mark>$1</mark>")}
                </div>`
                )
                .join("")}
            </div>
          `;
        });

        resultsDiv.innerHTML = html;
        resultsDiv.style.display = "block";
      }

      function displayEmailResults(data) {
        const resultsDiv = document.getElementById("results");
        let html = `
          <h3>Email & Phone Extraction Results</h3>
          <p><strong>Website:</strong> ${data.title}</p>
          <p><strong>URL:</strong> ${data.url}</p>
          <p><strong>Total Emails Found:</strong> ${data.total_emails}</p>
          <p><strong>Total Phone Numbers Found:</strong> ${data.total_phone_numbers}</p>
        `;

        if (data.emails && data.emails.length > 0) {
          html += `<h4>📧 Email Addresses:</h4>`;
          data.emails.forEach((emailData, index) => {
            html += `
              <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #28a745; border-radius: 4px; background: #f8fff9;">
                <strong>Email ${index + 1}:</strong>
                <span style="color: #28a745; font-weight: bold;">${
                  emailData.email
                }</span>
                <div style="margin-top: 5px; padding: 5px; background: #e9f7ef; font-size: 0.9em; border-radius: 3px;">
                  <strong>Context:</strong> ${emailData.context}
                </div>
              </div>
            `;
          });
        } else {
          html += `<p style="color: #dc3545;">No email addresses found on this page.</p>`;
        }

        if (data.phone_numbers && data.phone_numbers.length > 0) {
          html += `<h4>📞 Phone Numbers:</h4>`;
          data.phone_numbers.forEach((phoneData, index) => {
            html += `
              <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #007bff; border-radius: 4px; background: #f8f9ff;">
                <strong>Phone ${index + 1}:</strong>
                <span style="color: #007bff; font-weight: bold;">${
                  phoneData.phone
                }</span>
                <div style="margin-top: 5px; padding: 5px; background: #e7f3ff; font-size: 0.9em; border-radius: 3px;">
                  <strong>Context:</strong> ${phoneData.context}
                </div>
              </div>
            `;
          });
        } else {
          html += `<p style="color: #dc3545;">No phone numbers found on this page.</p>`;
        }

        resultsDiv.innerHTML = html;
        resultsDiv.style.display = "block";
      }

      function showError(message) {
        const resultsDiv = document.getElementById("results");
        resultsDiv.innerHTML = `<div class="error">${message}</div>`;
        resultsDiv.style.display = "block";
      }

      function showSuccess(message) {
        const resultsDiv = document.getElementById("results");
        resultsDiv.innerHTML = `<div class="success">${message}</div>`;
        resultsDiv.style.display = "block";
      }
    </script>
  </body>
</html>
