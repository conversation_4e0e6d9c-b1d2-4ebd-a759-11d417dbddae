import React, { useState } from 'react';

const ScrapingForm = ({ onScrape, onPreview, loading }) => {
  const [url, setUrl] = useState('');
  const [keywords, setKeywords] = useState('');
  const [previewData, setPreviewData] = useState(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!url.trim()) {
      alert('Please enter a URL');
      return;
    }

    if (!keywords.trim()) {
      alert('Please enter at least one keyword');
      return;
    }

    const keywordList = keywords
      .split(',')
      .map(k => k.trim())
      .filter(k => k.length > 0);

    if (keywordList.length === 0) {
      alert('Please enter valid keywords');
      return;
    }

    onScrape(url, keywordList);
  };

  const handlePreview = async () => {
    if (!url.trim()) {
      alert('Please enter a URL first');
      return;
    }

    const preview = await onPreview(url);
    if (preview) {
      setPreviewData(preview);
    }
  };

  const handleUrlChange = (e) => {
    setUrl(e.target.value);
    setPreviewData(null); // Clear preview when URL changes
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
            Website URL
          </label>
          <div className="flex space-x-2">
            <input
              type="url"
              id="url"
              value={url}
              onChange={handleUrlChange}
              placeholder="https://example.com"
              className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
            <button
              type="button"
              onClick={handlePreview}
              disabled={loading || !url.trim()}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Preview
            </button>
          </div>
        </div>

        {previewData && (
          <div className="bg-gray-50 rounded-md p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Website Preview</h3>
            <div className="text-sm text-gray-600">
              <p><strong>Title:</strong> {previewData.title}</p>
              {previewData.description && (
                <p><strong>Description:</strong> {previewData.description}</p>
              )}
              <p><strong>Word Count:</strong> {previewData.word_count?.toLocaleString()}</p>
            </div>
          </div>
        )}

        <div>
          <label htmlFor="keywords" className="block text-sm font-medium text-gray-700 mb-2">
            Keywords (comma-separated)
          </label>
          <input
            type="text"
            id="keywords"
            value={keywords}
            onChange={(e) => setKeywords(e.target.value)}
            placeholder="keyword1, keyword2, keyword3"
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            required
          />
          <p className="mt-1 text-sm text-gray-500">
            Enter keywords separated by commas. The scraper will find content containing these keywords.
          </p>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Scraping...
              </>
            ) : (
              'Start Scraping'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ScrapingForm;
